use maud::{html, <PERSON><PERSON>, DOCTYPE};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct StaffMember {
    pub name: &'static str,
    pub title: &'static str,
    pub tenure: Option<&'static str>,
}

#[derive(Debug, <PERSON>lone)]
pub struct TimelineItem {
    pub year: &'static str,
    pub status: &'static str,
    pub status_class: &'static str,
    pub project: &'static str,
    pub description: &'static str,
}

impl StaffMember {
    pub fn new(name: &'static str, title: &'static str) -> Self {
        Self {
            name,
            title,
            tenure: None,
        }
    }

    pub fn with_tenure(name: &'static str, title: &'static str, tenure: &'static str) -> Self {
        Self {
            name,
            title,
            tenure: Some(tenure),
        }
    }
}

impl TimelineItem {
    pub fn new(
        year: &'static str,
        status: &'static str,
        status_class: &'static str,
        project: &'static str,
        description: &'static str,
    ) -> Self {
        Self {
            year,
            status,
            status_class,
            project,
            description,
        }
    }
}

pub fn get_default_staff_members() -> Vec<StaffMember> {
    vec![
        StaffMember::with_tenure("<PERSON>", "President & Architect", "22 YEARS WITH APPCOVE"),
        StaffMember::new("<PERSON> Garber", "Director of Operations"),
        StaffMember::with_tenure("Andrew Bidochko", "Senior Engineer", "21 YEARS WITH APPCOVE"),
        StaffMember::with_tenure("Sergio Olivo", "Technical Project Consultant", "19 YEARS WITH APPCOVE"),
        StaffMember::new("Jeff Berdin", "Director of Infrastructure"),
        StaffMember::with_tenure("Iryna Bidochko", "Software Engineer", "10 YEARS WITH APPCOVE"),
        StaffMember::with_tenure("Jessi Garber", "Senior UI/UX Developer", "10 YEARS WITH APPCOVE"),
        StaffMember::new("Don Berdin", "Director of Support"),
    ]
}

pub fn get_default_timeline_items() -> Vec<TimelineItem> {
    vec![
        TimelineItem::new(
            "2004 — 21 Years",
            "(Active Project)",
            "active",
            "Advanced Reservation System",
            "Connected staff, guides, clients, guests, housekeeping together in one unified scheduling portal for an exclusive Pennsylvania Fishing club."
        ),
        TimelineItem::new(
            "2005 — 15 Years",
            "(Inactive Project)",
            "inactive",
            "CRM & Marketing Software",
            "Constructed a database system to collect and distribute millions of leads to thousands of small business owners nationwide via a unique filtering engine and CRM."
        ),
        TimelineItem::new(
            "2009 — 16 Years",
            "(Active Project)",
            "active",
            "Client Marketing Dashboard",
            "Built a custom marketing planning, community, and content delivery platform used by thousands of small businesses to access premium content, community, and the tools they need to grow their businesses."
        ),
        TimelineItem::new(
            "2013 — 12 Years",
            "(Active Project)",
            "active",
            "Technical Investment in ACRM",
            "Engineered and implemented a set of foundational modules that became the basis for all AppCove software going forward."
        ),
        TimelineItem::new(
            "2016 — 9 Years",
            "(Active Project)",
            "active",
            "Dashboard as a Service",
            "Designed a platform used by coaching organizations to serve their clients with content, training, community, and communications. Used by successful organizations in areas such as IT, Trades, Finance, and Law."
        ),
        TimelineItem::new(
            "2016 — 9 Years",
            "(Active Project)",
            "active",
            "Tools for Financial Advisors",
            "Created custom software representing unique finance and investment products. Hundreds of financial advisors have used these to serve tens of thousands of clients."
        ),
        TimelineItem::new(
            "2018 — 7 Years",
            "(Active Project)",
            "active",
            "Technical Investment in ACE",
            "Architected the AppCove Cloud Engine to consolidate cloud management, security, BDR, and deployment of custom software."
        ),
        TimelineItem::new(
            "2020 — 5 Years",
            "(Active Project)",
            "active",
            "Virtual Event Platform",
            "Deployed a scalable virtual event platform with custom graphical themes, live session streaming, interactive chat, video chat, help desk, breakout rooms, sponsor booths, broadcast studio, pre-recorded content, gamification, and real-time notifications."
        ),
        TimelineItem::new(
            "2023",
            "(Active Project)",
            "active",
            "Technical Investment in ACE 2.0",
            "ACE 2.0 is designed to cut through the complexity of the cloud, and deliver stable infrastructure to all custom applications we build going forward."
        ),
        TimelineItem::new(
            "2025",
            "(Active Project)",
            "active",
            "Technical Investment in approck",
            "Engineered and implemented our next generation toolbox of software infrastructure, dev/ops, and database management tools."
        ),
        TimelineItem::new(
            "2025",
            "(Active Project)",
            "active",
            "Fin/Tech Products",
            "Implementing next generation financial products combining credit reporting, insurance underwriting, debt payoff plans, community, and communications to be used by hundreds of financial professionals in assisting their clients."
        ),
    ]
}

/// Renders the complete AppCove homepage HTML document
pub fn render_homepage() -> Markup {
    render_homepage_with_data(&get_default_staff_members(), &get_default_timeline_items())
}

/// Renders the AppCove homepage with custom staff and timeline data
pub fn render_homepage_with_data(staff_members: &[StaffMember], timeline_items: &[TimelineItem]) -> Markup {
    html! {
        (DOCTYPE)
        html lang="en" {
            head {
                meta charset="UTF-8";
                title { "AppCove Overview" }
                link rel="stylesheet" href="index.css";
                link rel="preconnect" href="https://fonts.googleapis.com";
                link rel="preconnect" href="https://fonts.gstatic.com" crossorigin;
                link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet";
                link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css";
            }
            body {
                div class="page-container" {
                    div class="main-layout" {
                        (render_main_content(staff_members))
                        (render_timeline_sidebar(timeline_items))
                    }
                }
            }
        }
    }
}

/// Renders the main content section (left side)
fn render_main_content(staff_members: &[StaffMember]) -> Markup {
    html! {
        main class="main-content" {
            (render_hero_section())
            (render_company_info())
            (render_key_staff(staff_members))
            (render_contact_section())
        }
    }
}

/// Renders the hero section with illustration and company description
fn render_hero_section() -> Markup {
    html! {
        header class="hero-section" {
            figure class="hero-illustration" {
                img src="/images/AppCove.png" alt="AppCove Company Profile";
            }
            div class="hero-content" {
                h1 class="company-name" { "AppCove" }
                h2 class="tagline" {
                    "We build custom software focused on providing "
                    span class="highlight-customers" { "your customers" }
                    " with a premium experience."
                }
                p class="company-description" {
                    "AppCove has a proven track record of building and supporting reliable web applications for over 20 years. We take your long term business goals seriously."
                }
                p class="detailed-description" {
                    "This is reflected in every aspect of our work — from our people and training to our software and contracts. While you focus on innovation and operations, we focus on delivering technical excellence, robust security, and flexible infrastructure — whether hosted with us or deployed on your cloud."
                }
            }
        }
    }
}

/// Renders the company information section
fn render_company_info() -> Markup {
    html! {
        section class="company-info" aria-label="Company Information" {
            div class="info-item" {
                span class="info-label" { "FOUNDED" } " 2003"
            }
            div class="info-item" {
                span class="info-label" { "OWNER" } " Jason Garber"
            }
            div class="info-item" {
                span class="info-label" { "SIZE" } " 20+ Employees"
            }
            div class="info-item" {
                span class="info-label" { "HEADQUARTERS" } " Altoona, PA"
            }
        }
    }
}

/// Renders the key staff section
fn render_key_staff(staff_members: &[StaffMember]) -> Markup {
    html! {
        section class="key-staff" aria-labelledby="key-staff-heading" {
            h3 id="key-staff-heading" class="section-title" { "Key Staff" }
            div class="staff-grid" {
                @for staff in staff_members {
                    article class="staff-card" {
                        h4 class="staff-name" { (staff.name) }
                        div class="staff-title" { (staff.title) }
                        @if let Some(tenure) = staff.tenure {
                            div class="staff-tenure" { (tenure) }
                        }
                    }
                }
            }
        }
    }
}

/// Renders the contact section footer
fn render_contact_section() -> Markup {
    html! {
        footer class="contact-section" {
            h3 class="contact-title" { "FOR BUSINESS INQUIRES, CONTACT" }
            div class="contact-info" {
                address class="contact-details" {
                    div class="contact-person" {
                        div class="contact-name" { "Jason Garber" }
                        a href="mailto:<EMAIL>" class="contact-email" { "<EMAIL>" }
                        a href="tel:+***********" class="contact-phone" { "(*************" }
                    }
                    div class="contact-company" {
                        div class="company-name" { "AppCove, Inc." }
                        div class="company-address" { "P.O. Box 1309" }
                        div class="company-address" { "Altoona PA 16603" }
                    }
                }
            }
            div class="company-logo" {
                img src="/images/appcove_logo_2.png" alt="AppCove Logo";
            }
        }
    }
}

/// Renders the timeline sidebar (right side)
fn render_timeline_sidebar(timeline_items: &[TimelineItem]) -> Markup {
    html! {
        aside class="timeline-sidebar" aria-labelledby="timeline-heading" {
            header class="timeline-header" {
                h3 id="timeline-heading" class="timeline-title" { "Custom Software Deployment Timeline" }
                div class="timeline-subtitle" { "A SELECTION OF NOTABLE PROJECTS" }
            }
            div class="timeline-container" {
                @for item in timeline_items {
                    article class="timeline-item" {
                        div class="timeline-dot" aria-hidden="true" {}
                        time class="timeline-year" { (item.year) }
                        div class=(format!("timeline-status {}", item.status_class)) { (item.status) }
                        h4 class="timeline-project" { (item.project) }
                        p class="timeline-description" { (item.description) }
                    }
                }
            }
        }
    }
}

# AppCove Web Templates

This crate provides Maud templates for generating the AppCove homepage HTML.

## Features

- **Maud Templates**: Uses Rust's Maud templating engine for type-safe HTML generation
- **Structured Data**: Staff members and timeline items are defined as Rust structs
- **Customizable**: Templates can be rendered with custom data or use default values
- **Semantic HTML**: Preserves all semantic HTML5 elements and accessibility attributes
- **Complete Fidelity**: Generates identical HTML output to the original template

## Usage

### Basic Usage

```rust
use appcove_web::templates::render_homepage;

fn main() {
    let html = render_homepage();
    println!("{}", html.into_string());
}
```

### Custom Data

```rust
use appcove_web::templates::{render_homepage_with_data, StaffMember, TimelineItem};

let staff = vec![
    StaffMember::with_tenure("John Doe", "Senior Developer", "5 YEARS WITH APPCOVE"),
    StaffMember::new("<PERSON>", "Project Manager"),
];

let timeline = vec![
    TimelineItem::new(
        "2024",
        "(Active Project)",
        "active",
        "New Project",
        "Description of the new project."
    ),
];

let html = render_homepage_with_data(&staff, &timeline);
```

## Data Structures

### StaffMember

```rust
pub struct StaffMember {
    pub name: &'static str,
    pub title: &'static str,
    pub tenure: Option<&'static str>,
}
```

### TimelineItem

```rust
pub struct TimelineItem {
    pub year: &'static str,
    pub status: &'static str,
    pub status_class: &'static str,
    pub project: &'static str,
    pub description: &'static str,
}
```

## Building

```bash
cargo build
```

## Running the Example

```bash
cargo run --bin generate_html
```

This will output the complete HTML to stdout.

## Template Structure

The template is organized into several functions:

- `render_homepage()` - Main entry point with default data
- `render_homepage_with_data()` - Customizable version
- `render_main_content()` - Left side content
- `render_hero_section()` - Header with company info
- `render_company_info()` - Company details section
- `render_key_staff()` - Staff grid
- `render_contact_section()` - Footer contact info
- `render_timeline_sidebar()` - Right side timeline

## Dependencies

- `maud = "0.26"` - HTML templating engine

## Generated HTML

The template generates HTML that is functionally identical to the original `index.html`, including:

- All CSS classes and semantic HTML5 elements
- Accessibility attributes (aria-label, aria-labelledby, etc.)
- External resource links (Google Fonts, Font Awesome, CSS)
- Complete content preservation
- Proper HTML structure and nesting

#vim:fileencoding=utf-8:ts=4:sw=4:sts=4:expandtab

[workspace]
members = [
    "ace/agent/ace-agent",
    "ace/agent/ace-agent-updater",
    "ace/cli/ace",
    "ace/cli/ace-ssh",
    "ace/core/ace-aws",
    "ace/core/ace-check",
    "ace/core/ace-core",
    "ace/core/ace-db",
    "ace/core/ace-graph",
    "ace/core/ace-proc",
    "ace/server/ace-server",
    "ace/server/ace-server-agent",
    "ace/server/ace-server-api",
    "ace/server/ace-server-dashboard",
    "ace/server/ace-server-ssh",
    "ace/server/ace-server-zero",
    "ace/shared/ace-types",
    "ace/shared/garbage",
    "appcove/aplay",
    "appcove/appcove-appcove",
    "appcove/appcove-appcove-public",
    "appcove/appcove-appcove-zero",
    "appcove/appcove-web",
    "appcove/pm5-public",
    "appcove/pm5-zero",
    "appcove/pm5",
    "candoc/candoc",
    "candoc/candoc-bux",
    "demo/approck-example",
    "demo/approck-example-mod1",
    "demo/approck-example-mod2",
    "demo/demo-bux",
    "demo/demo-template",
    "demo/demo-uploader",
    "erg4/erg4",
    "homewaters/homewaters",
    "homewaters/homewaters-admin",
    "homewaters/homewaters-member",
    "homewaters/homewaters-public",
    "homewaters/homewaters-zero",
    "lib/acrm",
    "lib/approck",
    "lib/approck-ace",
    "lib/approck-acme",
    "lib/approck-compiler",
    "lib/approck-macros",
    "lib/approck-memfs",
    "lib/approck-postgres",
    "lib/approck-redis",
    "lib/approck-server",
    "lib/appstruct",
    "lib/bux",
    "lib/bux-compiler",
    "lib/bux-macros",
    "lib/chasetls",
    "lib/granite",
    "lib/granite-compiler",
    "lib/granite-core",
    "lib/granite-macros",
    "lib/meta/acp",
    "lib/meta/acp-config",
    "lib/meta/acp-init",
    "lib/pgmig",
    "module/addr-iso",
    "module/api-mailgun",
    "module/api-sendgrid",
    "module/api-sentry",
    "module/api-stripe",
    "module/api-twilio",
    "module/auth-fence",
    "module/auth-fence-provider",
    "module/intake-silo",
    "module/legal-plane",
    "module/msg-io",
    "module/msg-io-email",
    "module/msg-io-sms",
    "reo/reo",
    "reo/reo-admin",
    "reo/reo-client",
    "reo/reo-packet",
    "reo/reo-public",
    "reo/reo-staff",
    "reo/reo-user",
    "reo/reo-zero",
    "smart/df4l",
    "smart/df4l-admin",
    "smart/df4l-advisor",
    "smart/df4l-crs",
    "smart/df4l-icover",
    "smart/df4l-public",
    "smart/df4l-zero",
    "smart/rrr",
    "smart/rrr-admin",
    "smart/rrr-member",
    "smart/rrr-public",
    "smart/rrr-zero",
    "tmt/mediaproctor", 
]


resolver = "2"

[workspace.dependencies]
acp-config = { path = "lib/meta/acp-config" }
acrm = { path = "lib/acrm" }
addr-iso = { path = "module/addr-iso" }
api-mailgun = { path = "module/api-mailgun" }
api-sendgrid = { path = "module/api-sendgrid" }
api-sentry = { path = "module/api-sentry" }
api-stripe = { path = "module/api-stripe" }
api-twilio = { path = "module/api-twilio" }
approck = { path = "lib/approck" }
approck-compiler = { path = "lib/approck-compiler" }
approck-memfs = { path = "lib/approck-memfs" }
approck-postgres = { path = "lib/approck-postgres" }
approck-redis = { path = "lib/approck-redis" }
approck-server = { path = "lib/approck-server" }
appstruct = { path = "lib/appstruct" }
auth-fence = { path = "module/auth-fence" }
auth-fence-provider = { path = "module/auth-fence-provider" }
aws-config = { git = "https://github.com/appcove/acp7r-aws-sdk.git", tag = "acp7-smithy-rs-v0.45.0" }
aws-sdk-ec2 = { git = "https://github.com/appcove/acp7r-aws-sdk.git", tag = "acp7-smithy-rs-v0.45.0" }
aws-sdk-route53 = { git = "https://github.com/appcove/acp7r-aws-sdk.git", tag = "acp7-smithy-rs-v0.45.0" }
bux = { path = "lib/bux" }
chasetls = { path = "lib/chasetls" }
granite = { path = "lib/granite" }
granite-compiler = { path = "lib/granite-compiler" }
intake-silo = { path = "module/intake-silo" }
legal-plane = { path = "module/legal-plane" }
msg-io = { path = "module/msg-io" }
msg-io-email = { path = "module/msg-io-email" }
msg-io-sms = { path = "module/msg-io-sms" }
pgmig = { path = "lib/pgmig" }



# Additional dependencies from individual crates
aes-gcm = "0.10.3"
anyhow = "1.0.70"
async-trait = "0.1.77"
atty = "0.2.14"
base64_light = "0.1.5"
bb8 = "0.8.3"
bb8-postgres = "0.8.1"
bb8-redis = "0.15.0"
boxcar = "0.2.11"
bytes = "1.5.0"
chrono = "0.4.35"
chrono-tz = "0.10"
clap = "4.2.4"
clap-markdown = "0.1.3"
comfy-table = "7.0.1"
console = "0.15.7"
cookie = "0.18.1"
dashmap = "6.1.0"
data-encoding = "2.4.0"
dialoguer = "0.11.0"
dirs = "6.0.0"
dotenv = "0.15.0"
dprint-plugin-json = "0.20.0"
dprint-plugin-typescript = "0.94.0"
error-stack = "0.4.1"
flate2 = "1.0.27"
form_urlencoded = "1.2.1"
fs-more = "0.8.0"
futures = "0.3.28"
futures-util = "0.3.30"
git2 = "0.20.2"
glob = "0.3.1"
hcl-rs = "0.18.5"
headers = "0.4.0"
hostname = "0.4.0"
html-escape = "0.2.13"
http = "1.1.0"
http-body-util = "0.1.2"
hyper = "1.3.1"
hyper-rustls = "0.27.2"
hyper-tungstenite = "0.15.0"
hyper-util = "0.1.3"
ignore = "0.4.22"
indexmap = "2.2.6"
indicatif = "0.17.3"
instant-acme = "0.7.1"
ipnetwork = "0.21.1"
iso8601-timestamp = "0.3.3"
itertools = "0.14.0"
json = "0.12.4"
lightningcss = "1.0.0-alpha.66"
maud = "0.27.0"
mime = "0.3.17"
nix = "0.30.1"
num_cpus = "1.16.0"
num-bigint = "0.4.5"
num-format = "0.4.0"
num-traits = "0.2.17"
oauth2 = { version = "5.0", features = ["reqwest"] }
once_cell = "1.19.0"
openidconnect = "4.0.0"
openssl = "0.10.56"
pbkdf2 = "0.12.2"
petgraph = "0.8.1"
phf = "0.11.1"
phf_codegen = "0.11.1"
postgres-types = { version = "0.2.8", features = ["derive"] }
prettyplease = { version = "0.2.29" }
proc-macro2 = "1.0.67"
proc-macro2-diagnostics = "0.10.1"
pulldown-cmark = "0.13.0"
quote = "1.0.33"
rand = "0.8.5"
rcgen = { version = "0.13.1", default-features = false, features = ["aws_lc_rs", "pem", "crypto"] }
redis = "0.25.2"
regex = "1.8.3"
reqwest = "0.12.9"
rust_decimal = "1.35.0"
rust_decimal_macros = "1.34.2"
rustls = { version = "0.23.12", features = ["aws_lc_rs"] }
rustls-pemfile = "2.1.2"
semver = "1.0.23"
sentry = "0.34.0"
serde = "1.0.193"
serde_json = "1.0.110"
serde_qs = "0.12.0"
serde_toml = "0.0.1"
serde_with = "3.0.0"
serde_yaml = "0.9.21"
sha2 = "0.10.8"
shell-quote = "0.3.0"
shell-words = "1.1.0"
ssh-key = "0.6.6"
sxd-document = "0.3.2"
syn = "2.0.37"
sysinfo = "0.35.1"
tar = "0.4.40"
tempfile = "3.8.1"
termcolor = "1.4.1"
thiserror = "1.0.43"
tokio = "1.39.3"
tokio-postgres = "0.7.10"
tokio-rustls = "0.26.0"
tokio-stream = "0.1.16"
tokio-tungstenite = "0.24.0"
toml = "0.8.14"
tracing = "0.1.37"
tracing-subscriber = "0.3.17"
trust-dns-client = "0.23.2"
trust-dns-proto = "0.23.2"
trust-dns-resolver = "0.23.2"
tungstenite = "0.24.0"
ureq = "2.8.0"
url = "2.5.0"
urlencoding = "2.1.2"
uuid = { version = "1.8.0", features = ["v4", "v7", "serde"] }
walkdir = "2.3.3"
x509-parser = "0.16.0"
zip = "3.0.0"
